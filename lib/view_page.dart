import 'package:flutter/material.dart';
import 'package:my_chef/pages/main_page.dart';
import 'package:my_chef/pages/profile_page.dart';
import 'package:my_chef/pages/recipy_page/recipy_list_page.dart';
import 'package:sliding_clipped_nav_bar/sliding_clipped_nav_bar.dart';

class ViewPage extends StatefulWidget {
  const ViewPage({super.key});

  @override
  State<ViewPage> createState() => _ViewPageState();
}

class _ViewPageState extends State<ViewPage> {
  late final PageController _pageController;
  int selectedIndex = 1;

  @override
  void initState() {
    _pageController = PageController(
      initialPage: 1,
    );
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: true,
        child: PageView(
          physics: NeverScrollableScrollPhysics(),
          controller: _pageController,
          children: [
            RecipyListPage(),
            MainPage(),
            ProfilePage(),
          ],
        ),
      ),
      bottomNavigationBar: SlidingClippedNavBar(
        backgroundColor: Colors.white,
        onButtonPressed: (index) {
          setState(() {
            selectedIndex = index;
          });
          _pageController.animateToPage(selectedIndex,
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeOutQuad);
        },
        iconSize: 30,
        activeColor: Color(0xFF01579B),
        selectedIndex: selectedIndex,
        barItems: [
          BarItem(
            icon: Icons.list_sharp,
            title: 'List',
          ),
          BarItem(
            icon: Icons.home_sharp,
            title: 'Home',
          ),
          BarItem(
            icon: Icons.person_sharp,
            title: 'Profile',
          ),
        ],
      ),
    );
  }
}
