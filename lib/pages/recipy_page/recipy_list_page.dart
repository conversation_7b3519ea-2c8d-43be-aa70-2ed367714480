import 'package:flutter/material.dart';

class RecipyListPage extends StatefulWidget {
  const RecipyListPage({super.key});

  @override
  State<RecipyListPage> createState() => _RecipyListPageState();
}

class _RecipyListPageState extends State<RecipyListPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            child: <PERSON>umn(
              children: [],
            ),
          )
        ],
      ),
    );
  }
}
