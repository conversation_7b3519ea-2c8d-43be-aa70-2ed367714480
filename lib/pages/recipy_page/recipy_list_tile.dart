import 'package:flutter/material.dart';

class RecipyListTileModel {
  final String uuid;
  final String title;
  final double score;
  final int time;
  final bool like;

  RecipyListTileModel({
    required this.uuid,
    required this.title,
    required this.score,
    required this.time,
    required this.like,
  });
}

class RecipyListTile extends StatelessWidget {
  final RecipyListTileModel recipyListTileModel;
  const RecipyListTile({
    super.key,
    required this.recipyListTileModel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(children: [
        Row(
          children: [],
        ),
      ]),
    );
  }
}
