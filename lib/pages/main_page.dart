import 'package:flutter/material.dart';
import 'package:my_chef/constants.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  String nickname = '냉털';
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: defaultPadding,
            child: Column(
              children: [
                Row(
                  children: [
                    Column(
                      children: [
                        Text(
                          '알뜰 냉털하는 ${nickname}님,',
                        ),
                        Text(
                          '오늘도 냉털과 함께 야무지게 먹어봐요!',
                        ),
                      ],
                    ),
                    Spacer(),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.amber,
                      ),
                      height: 50,
                      width: 50,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                height: 100,
                width: MediaQuery.of(context).size.width,
                child: Text('sdf'),
              )
            ],
          )
        ],
      ),
    );
  }
}
