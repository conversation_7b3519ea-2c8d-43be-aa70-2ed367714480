name: my_chef
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.1.1+6

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  app_links: 6.2.0
  auto_size_text: 3.0.0
  cached_network_image: 3.4.0
  equatable: 2.0.5
  flutter_animate: 4.5.0
  flutter_cache_manager: 3.4.0
  flutter_rating_bar: 4.0.1
  font_awesome_flutter: 10.7.0
  from_css_color: 2.0.0
  functions_client: 2.3.2
  go_router: 9.0.2
  google_fonts: 6.2.1
  google_mobile_ads: 5.1.0
  gotrue: 2.8.4
  lottie: 3.1.2
  mime_type: 1.0.0
  page_transition: 2.1.0
  path_provider: 2.1.4
  path_provider_android: 2.2.10
  path_provider_foundation: 2.4.0
  path_provider_platform_interface: 2.1.2
  postgrest: 2.1.4
  provider: 6.1.2
  realtime_client: 2.2.1
  shared_preferences: 2.3.1
  shared_preferences_android: 2.3.1
  shared_preferences_foundation: 2.5.2
  shared_preferences_platform_interface: 2.4.1
  shared_preferences_web: 2.4.2
  sign_in_with_apple: 6.1.1
  sign_in_with_apple_platform_interface: 1.1.0
  sign_in_with_apple_web: 2.1.0
  sqflite: ^2.4.2
  storage_client: 2.0.3
  substring_highlight: 1.0.33
  supabase: 2.3.0
  supabase_flutter: 2.6.0
  timeago: 3.7.0
  url_launcher: 6.3.0
  url_launcher_android: 6.3.8
  url_launcher_ios: 6.3.1
  url_launcher_platform_interface: 2.3.2
  webview_flutter: 4.8.0
  webview_flutter_android: 3.16.6
  webview_flutter_platform_interface: 2.10.0
  webview_flutter_wkwebview: 3.14.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Oibori added
  showcaseview: ^4.0.1
  unsplash_client: ^2.2.0
  auto_animated: ^3.2.0
  uuid: ^4.5.1
  flutter_i18n: ^0.36.3
  sliding_clipped_nav_bar: ^3.1.1

dependency_overrides:
  rxdart: 0.27.7

dev_dependencies:
  flutter_launcher_icons: 0.13.1
  flutter_lints: 4.0.0
  image: 4.2.0
  lints: 4.0.0

  flutter_test:
    sdk: flutter

flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  web:
    generate: true
  image_path: "assets/images/app_launcher_icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/i18n/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
